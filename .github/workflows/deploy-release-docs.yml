name: Deploy Release API Documentation to Netlify

on:
  release:
    types: [published]

jobs:
  deploy-api-docs:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install documentation tools
      run: |
        npm install -g @redocly/cli

    - name: Validate API specification
      run: |
        npx @redocly/cli lint API/ApiDefinition.yaml

    - name: Generate documentation site
      run: |
        mkdir -p mail

        # Generate interactive API documentation
        npx @redocly/cli build-docs API/ApiDefinition.yaml --output=mail/index.html

        # Copy API specification
        cp API/ApiDefinition.yaml mail/api-specification.yaml

    - name: Create additional site pages
      run: |
        # Create a simple about page with release information
        cat > mail/about.html << 'EOF'
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>About - Atvero Email Filing API</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 2rem;
                    line-height: 1.6;
                    color: #333;
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 2rem;
                    border-radius: 8px;
                    margin-bottom: 2rem;
                }
                .info-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 1rem 0;
                }
                .info-table th, .info-table td {
                    padding: 0.75rem;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                .info-table th {
                    background-color: #f8f9fa;
                    font-weight: 600;
                }
                .nav-links {
                    display: flex;
                    gap: 1rem;
                    margin: 2rem 0;
                    flex-wrap: wrap;
                }
                .nav-link {
                    background: #007bff;
                    color: white;
                    padding: 0.75rem 1.5rem;
                    text-decoration: none;
                    border-radius: 4px;
                    transition: background 0.2s;
                }
                .nav-link:hover {
                    background: #0056b3;
                }
                .features {
                    background: #f8f9fa;
                    padding: 1.5rem;
                    border-radius: 8px;
                    margin: 2rem 0;
                }
                .features ul {
                    margin: 0;
                    padding-left: 1.5rem;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 Atvero Email Filing API Documentation</h1>
                <p><strong>Release ${{ github.event.release.tag_name }}</strong> - Published on ${{ github.event.release.published_at }}</p>
            </div>

            <div class="nav-links">
                <a href="index.html" class="nav-link">📖 Interactive API Documentation</a>
                <a href="api-specification.yaml" class="nav-link">📄 OpenAPI Specification</a>
                <a href="${{ github.event.release.html_url }}" class="nav-link">🏷️ Release Notes</a>
                <a href="${{ github.event.repository.html_url }}" class="nav-link">📂 Source Repository</a>
            </div>

            <h2>📋 Release Information</h2>
            <table class="info-table">
                <tr><th>Version</th><td>${{ github.event.release.tag_name }}</td></tr>
                <tr><th>Released</th><td>${{ github.event.release.published_at }}</td></tr>
                <tr><th>Commit</th><td><code>${{ github.sha }}</code></td></tr>
                <tr><th>Repository</th><td><a href="${{ github.event.repository.html_url }}">${{ github.repository }}</a></td></tr>
            </table>

            <div class="features">
                <h2>✨ Features</h2>
                <ul>
                    <li>Complete API endpoint documentation</li>
                    <li>Request/response schemas with validation</li>
                    <li>Interactive examples for testing</li>
                    <li>Authentication and error handling guides</li>
                </ul>

                <h2>🔧 Technical Details</h2>
                <ul>
                    <li>Built with OpenAPI 3.0.3 specification</li>
                    <li>Generated using Redoc for interactive documentation</li>
                    <li>Automatically deployed via GitHub Actions to Netlify</li>
                </ul>
            </div>

            <hr>
            <p><em>📝 This documentation was automatically generated from release ${{ github.event.release.tag_name }}</em></p>
        </body>
        </html>
        EOF

        # Create a _redirects file for Netlify
        cat > mail/_redirects << 'EOF'
        # Redirect /about to /about.html
        /about /about.html 200

        # Redirect /spec to the API specification
        /spec /api-specification.yaml 200

        # Redirect /specification to the API specification
        /specification /api-specification.yaml 200
        EOF

        # Create a netlify.toml configuration file
        cat > mail/netlify.toml << 'EOF'
        [build]
          publish = "."

        [build.environment]
          NODE_VERSION = "18"

        [[headers]]
          for = "/*"
          [headers.values]
            X-Frame-Options = "DENY"
            X-XSS-Protection = "1; mode=block"
            X-Content-Type-Options = "nosniff"
            Referrer-Policy = "strict-origin-when-cross-origin"

        [[headers]]
          for = "*.yaml"
          [headers.values]
            Content-Type = "application/x-yaml"

        [[headers]]
          for = "*.yml"
          [headers.values]
            Content-Type = "application/x-yaml"
        EOF

    - name: Deploy to Netlify
      uses: nwtgck/actions-netlify@v3.0
      with:
        publish-dir: './mail'
        production-branch: main
        github-token: ${{ secrets.GITHUB_TOKEN }}
        deploy-message: "Deploy API documentation for release ${{ github.event.release.tag_name }}"
        enable-pull-request-comment: false
        enable-commit-comment: false
        overwrites-pull-request-comment: false
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.API_DOC_SITE_ID }}
      id: netlify-deploy
